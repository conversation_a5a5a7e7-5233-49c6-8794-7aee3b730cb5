{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "vite": "^7.0.4"}, "dependencies": {"@headlessui/react": "^2.2.6", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@tailwindcss/postcss": "^4.1.11", "axios": "^1.11.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.61.1", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.7.0", "socket.io-client": "^4.8.1", "yup": "^1.6.1", "zustand": "^5.0.6"}}