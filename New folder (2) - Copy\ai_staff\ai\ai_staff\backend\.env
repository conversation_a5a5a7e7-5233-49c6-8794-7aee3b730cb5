# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
# Local MongoDB (install MongoDB first)
# MONGODB_URI=mongodb://localhost:27017/virtual-ai-teacher

# MongoDB Atlas (cloud) - Replace with your actual connection string
MONGODB_URI=mongodb+srv://24124046:Ragul@<EMAIL>/

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRE=7d
JWT_REFRESH_SECRET=your-refresh-token-secret-change-this-too
JWT_REFRESH_EXPIRE=30d

# CORS Configuration
FRONTEND_URL=http://localhost:5173
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,jpg,jpeg,png

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Security
BCRYPT_SALT_ROUNDS=12
