/**
 * Student Dashboard Component for Virtual AI Teacher Platform
 */

import React from 'react';

const StudentDashboard = () => {
  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Student Dashboard</h1>
        <p className="text-gray-600">Your learning journey starts here</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Enrolled Courses</h3>
          <p className="text-3xl font-bold text-blue-600">6</p>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">AI Chats</h3>
          <p className="text-3xl font-bold text-green-600">23</p>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Progress</h3>
          <p className="text-3xl font-bold text-purple-600">78%</p>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Learning Overview</h2>
        <p className="text-gray-600">Welcome to your learning dashboard. Access your courses, chat with AI teachers, and track your progress.</p>
      </div>
    </div>
  );
};

export default StudentDashboard;
