{"version": 3, "file": "normalize-windows-path.js", "sourceRoot": "", "sources": ["../../src/normalize-windows-path.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,gDAAgD;AAChD,sEAAsE;AACtE,qEAAqE;;;AAErE,MAAM,QAAQ,GACZ,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,OAAO,CAAC,QAAQ,CAAA;AAE9C,QAAA,oBAAoB,GAC/B,QAAQ,KAAK,OAAO,CAAC,CAAC;IACpB,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA", "sourcesContent": ["// on windows, either \\ or / are valid directory separators.\n// on unix, \\ is a valid character in filenames.\n// so, on windows, and only on windows, we replace all \\ chars with /,\n// so that we can use / as our one and only directory separator char.\n\nconst platform =\n  process.env.TESTING_TAR_FAKE_PLATFORM || process.platform\n\nexport const normalizeWindowsPath =\n  platform !== 'win32' ?\n    (p: string) => p\n  : (p: string) => p && p.replace(/\\\\/g, '/')\n"]}