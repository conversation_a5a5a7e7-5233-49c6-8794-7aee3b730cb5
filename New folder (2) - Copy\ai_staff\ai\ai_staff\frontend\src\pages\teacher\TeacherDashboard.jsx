/**
 * Teacher Dashboard Component for Virtual AI Teacher Platform
 */

import React from 'react';

const TeacherDashboard = () => {
  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Teacher Dashboard</h1>
        <p className="text-gray-600">Manage your classes and content</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">My Classes</h3>
          <p className="text-3xl font-bold text-blue-600">5</p>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Students</h3>
          <p className="text-3xl font-bold text-green-600">120</p>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Content Items</h3>
          <p className="text-3xl font-bold text-purple-600">45</p>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Teaching Overview</h2>
        <p className="text-gray-600">Welcome to your teaching dashboard. Manage your classes, upload content, and track student progress.</p>
      </div>
    </div>
  );
};

export default TeacherDashboard;
