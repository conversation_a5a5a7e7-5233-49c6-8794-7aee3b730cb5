/**
 * HOD Dashboard Component for Virtual AI Teacher Platform
 */

import React from 'react';

const HODDashboard = () => {
  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">HOD Dashboard</h1>
        <p className="text-gray-600">Department management and oversight</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Teachers</h3>
          <p className="text-3xl font-bold text-blue-600">24</p>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Students</h3>
          <p className="text-3xl font-bold text-green-600">456</p>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Courses</h3>
          <p className="text-3xl font-bold text-purple-600">12</p>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Department Overview</h2>
        <p className="text-gray-600">Welcome to your department dashboard. Manage teachers, students, and courses from here.</p>
      </div>
    </div>
  );
};

export default HODDashboard;
